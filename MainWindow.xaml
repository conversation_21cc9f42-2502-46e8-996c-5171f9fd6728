<Window x:Class="RaindropHub.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:RaindropHub"
        xmlns:viewModel="clr-namespace:RaindropHub.ViewModel"
        mc:Ignorable="d"
        Background="#FF1E1E1E"
        ResizeMode="CanMinimize"
        Icon="Icon/rain.ico"
        Title="RaindropHub" Height="500" Width="900"
        WindowStartupLocation="CenterScreen">
    <Window.DataContext>
        <viewModel:MainViewModel/>
    </Window.DataContext>

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="100"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>
        <Grid Background="Transparent">
            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="50"/>
                <RowDefinition Height="50"/>
            </Grid.RowDefinitions>
            <RadioButton Grid.Row="0" IsChecked="True" Content="Home" Style="{StaticResource SideBarButtonStyle}" Command="{Binding ShowHomeViewCommand}"></RadioButton>
            <RadioButton Grid.Row="1" Content="Controller" Style="{StaticResource SideBarButtonStyle}" Command="{Binding ShowControlViewCommand}"></RadioButton>
            <RadioButton Grid.Row="2" Content="Settings" Style="{StaticResource SideBarButtonStyle}" Command="{Binding ShowSettingViewCommand}"></RadioButton>
        </Grid>
        <ContentControl Grid.Column="1" Content="{Binding CurrentView}"/>
    </Grid>
</Window>