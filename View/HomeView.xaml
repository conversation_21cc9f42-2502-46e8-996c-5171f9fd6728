<UserControl x:Class="RaindropHub.HomeView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:RaindropHub"
             mc:Ignorable="d"
             Background="Transparent"
             d:DesignHeight="500" d:DesignWidth="800">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="450"></ColumnDefinition>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        <DataGrid Name="AccountDataGrid" Margin="1"
                  SelectionChanged="AccountSelectionChanged"
                  HeadersVisibility="Column"
                  IsReadOnly="True"
                  AutoGenerateColumns="False"
                  CanUserResizeColumns="False"
                  Background="Transparent"
                  CanUserSortColumns="False"
                  CanUserReorderColumns="False"
                  ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                  ScrollViewer.VerticalScrollBarVisibility="Disabled"
                  VirtualizingStackPanel.IsVirtualizing="False"
                  VirtualizingStackPanel.VirtualizationMode="Standard">
            <DataGrid.Columns>
                <DataGridTextColumn Width="50" Header="STT" Binding="{Binding Id}" />
                <DataGridTextColumn Width="150" Header="Tài khoản" Binding="{Binding Username}" />
                <DataGridTextColumn Width="50" Header="Server" Binding="{Binding Server}" />
                <DataGridTextColumn Width="100" Header="Type" Binding="{Binding Type}" />
                <DataGridTextColumn Width="100" Header="Size" Binding="{Binding Size}" />
                <DataGridTemplateColumn Width="50" Header="Item">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <Button Content="Xem" Click="ViewButton_Click"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
            </DataGrid.Columns>
        </DataGrid>
        <Grid Grid.Column="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition></ColumnDefinition>
                <ColumnDefinition></ColumnDefinition>
                <ColumnDefinition></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <StackPanel Grid.Column="0" Margin="0,0,0,0">
                <Grid Margin="0,0,0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"></ColumnDefinition>
                        <ColumnDefinition Width="200"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <Label Foreground="Gold" Content="Tài khoản" Width="70"></Label>
                    <TextBox Grid.Column="1" Name="txtUsername" Style="{StaticResource TextBoxInfoStyle}"></TextBox>
                </Grid>
                <Grid Margin="0,0,0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"></ColumnDefinition>
                        <ColumnDefinition Width="200"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <Label Foreground="Gold" Content="Mật khẩu" Width="70"></Label>
                    <PasswordBox Grid.Column="1" Name="txtPassword" Style="{StaticResource PassBoxInfoStyle}"></PasswordBox>
                </Grid>
                <Grid Margin="0,0,0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"></ColumnDefinition>
                        <ColumnDefinition Width="200"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <Label Foreground="Gold" Content="Server" Width="70"></Label>
                    <ComboBox Grid.Column="1" Name="ServerComboBox" Style="{StaticResource ComboBoxStyle}">
                        <ComboBoxItem IsSelected="True">Vũ trụ 7</ComboBoxItem>
                    </ComboBox>
                </Grid>
                <Grid Margin="0,0,0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"></ColumnDefinition>
                        <ColumnDefinition Width="200"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <Label Foreground="Gold" Content="Vai trò" Width="70"></Label>
                    <ComboBox Grid.Column="1" Name="RoleComboBox" Style="{StaticResource ComboBoxStyle}">
                        <ComboBoxItem>Normal</ComboBoxItem>
                        <ComboBoxItem>AutoPet</ComboBoxItem>
                        <ComboBoxItem>GetSenzu</ComboBoxItem>
                        <ComboBoxItem>GetSenzuV2</ComboBoxItem>
                        <ComboBoxItem IsSelected="True">SendSenzu</ComboBoxItem>
                    </ComboBox>
                </Grid>
                <Grid Margin="0,0,0,5">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"></ColumnDefinition>
                        <ColumnDefinition Width="200"></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <Label Foreground="Gold" Content="Size" Width="70"></Label>
                    <ComboBox Grid.Column="1" Name="SizeComboBox" Style="{StaticResource ComboBoxStyle}">
                        <ComboBoxItem>1x1</ComboBoxItem>
                        <ComboBoxItem>100x100</ComboBoxItem>
                        <ComboBoxItem>480x320</ComboBoxItem>
                        <ComboBoxItem>1024x600</ComboBoxItem>
                        <ComboBoxItem IsSelected="True">200x200</ComboBoxItem>
                    </ComboBox>
                </Grid>
                <Grid Margin="0,5,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition></ColumnDefinition>
                        <ColumnDefinition></ColumnDefinition>
                        <ColumnDefinition></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <Button Content="Login" Style="{StaticResource AddButtonStyle}" Click="LoginButton_Click"></Button>
                    <Button Grid.Column="1" Content="Lưu" Style="{StaticResource UpdateButtonStyle}"
                            Click="UpdateButton_Click">
                    </Button>
                    <Button Grid.Column="2" Content="Xóa" Style="{StaticResource DeleteButtonStyle}"
                            Click="DeleteButton_Click">
                    </Button>
                </Grid>
                <Grid Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition></ColumnDefinition>
                        <ColumnDefinition></ColumnDefinition>
                        <ColumnDefinition></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <Button Content="Start" Style="{StaticResource AddButtonStyle}" Click="StartButton_Click"></Button>
                    <Button Grid.Column="1" Content="Stop" Style="{StaticResource UpdateButtonStyle}"
                            Click="StopButton_Click">
                    </Button>
                    <Button Grid.Column="2" Content="Close All" Style="{StaticResource DeleteButtonStyle}"
                            Click="CloseAllButton_Click">
                    </Button>
                </Grid>
                <Grid Margin="0,10,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition></ColumnDefinition>
                        <ColumnDefinition></ColumnDefinition>
                        <ColumnDefinition></ColumnDefinition>
                    </Grid.ColumnDefinitions>
                    <Button Content="Sort" Style="{StaticResource AddButtonStyle}" Click="SortButton_Click"></Button>
                    <Button Grid.Column="1" Content="Auto Hide: Off" Style="{StaticResource UpdateButtonStyle}"
                            Click="AutoHideButton_Click">
                    </Button>
                    <Button Grid.Column="2" Content="AClose: Off" Style="{StaticResource DeleteButtonStyle}"
                            Click="AutoCloseButton_Click">
                    </Button>
                </Grid>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>